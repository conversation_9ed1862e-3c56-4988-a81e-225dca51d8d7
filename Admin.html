<!DOCTYPE html>
<html lang="en">

<head>

    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="">
    <meta name="author" content="">

    <title>Dashboard</title>
    <link rel="shortcut icon" type="image/x-icon" href="assets/img/BINHSLogo.ico">

    <!-- Custom fonts for this template-->
    <link href="assets/admin/vendor/fontawesome-free/css/all.min.css" rel="stylesheet" type="text/css">
    <link
        href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i"
        rel="stylesheet">

    <!-- Custom styles for this template-->
    <link href="assets/admin/css/sb-admin-2.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">

    <!-- Mobile Responsive Styles -->
    <link href="assets/css/responsive.css" rel="stylesheet">

    <!-- Custom CSS for lime green sidebar -->
    <style>
        /* Override sidebar background to lime green */
        .sidebar.bg-gradient-primary {
            background-color: #32CD32 !important;
            background-image: linear-gradient(180deg, #32CD32 10%, #228B22 100%) !important;
            background-size: cover;
        }

        /* Ensure sidebar brand and nav items have proper contrast */
        .sidebar.bg-gradient-primary .sidebar-brand {
            color: #ffffff !important;
        }

        .sidebar.bg-gradient-primary .nav-item .nav-link {
            color: rgba(255, 255, 255, 0.8) !important;
        }

        .sidebar.bg-gradient-primary .nav-item .nav-link:hover,
        .sidebar.bg-gradient-primary .nav-item .nav-link:focus,
        .sidebar.bg-gradient-primary .nav-item.active .nav-link {
            color: #ffffff !important;
        }

        .sidebar.bg-gradient-primary .nav-item .nav-link i {
            color: rgba(255, 255, 255, 0.8) !important;
        }

        .sidebar.bg-gradient-primary .nav-item .nav-link:hover i,
        .sidebar.bg-gradient-primary .nav-item .nav-link:focus i,
        .sidebar.bg-gradient-primary .nav-item.active .nav-link i {
            color: #ffffff !important;
        }

        .sidebar.bg-gradient-primary .sidebar-divider {
            border-top: 1px solid rgba(255, 255, 255, 0.15) !important;
        }

        .sidebar.bg-gradient-primary #sidebarToggle {
            background-color: rgba(255, 255, 255, 0.2) !important;
        }

        .sidebar.bg-gradient-primary #sidebarToggle:hover {
            background-color: rgba(255, 255, 255, 0.3) !important;
        }

        /* Make the dashboard logo more noticeable */
        .sidebar-brand-icon {
            background: rgba(255, 255, 255, 0.95) !important;
            border-radius: 50% !important;
            padding: 8px !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3), 0 2px 6px rgba(0, 0, 0, 0.2) !important;
            border: 2px solid rgba(255, 255, 255, 0.8) !important;
            transition: all 0.3s ease !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
        }

        .sidebar-brand-icon img {
            filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2)) !important;
            transition: all 0.3s ease !important;
            border-radius: 50% !important;
        }

        /* Enhanced effects on hover */
        .sidebar-brand:hover .sidebar-brand-icon {
            background: rgba(255, 255, 255, 1) !important;
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4), 0 3px 8px rgba(0, 0, 0, 0.3) !important;
            border: 2px solid #ffffff !important;
            transform: scale(1.05) !important;
        }

        .sidebar-brand:hover .sidebar-brand-icon img {
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3)) !important;
        }

        /* Mobile sidebar enhancements */
        @media (max-width: 767.98px) {
            /* Ensure topbar toggle is visible on mobile */
            #sidebarToggleTop {
                display: block !important;
            }

            /* Hide sidebar toggle inside sidebar on mobile */
            .sidebar #sidebarToggle {
                display: none !important;
            }

            /* Mobile sidebar positioning and styling */
            .sidebar {
                z-index: 1050 !important;
                position: fixed !important;
                top: 0;
                left: -250px !important; /* Start hidden */
                width: 250px !important;
                height: 100vh;
                transition: left 0.3s ease-in-out;
                overflow-y: auto;
                background: #32CD32 !important;
                background-image: linear-gradient(180deg, #32CD32 10%, #228B22 100%) !important;
            }

            /* Show sidebar when NOT toggled (sb-admin-2.js removes 'toggled' class to show) */
            .sidebar:not(.toggled) {
                left: 0 !important;
            }

            /* Ensure sidebar is hidden when toggled */
            .sidebar.toggled {
                left: -250px !important;
            }

            /* Content wrapper adjustments for mobile */
            #content-wrapper {
                margin-left: 0 !important;
                width: 100% !important;
            }

            /* Mobile overlay for sidebar */
            .sidebar-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                z-index: 1040;
                display: none;
            }

            .sidebar-overlay.show {
                display: block;
            }

            /* Mobile topbar adjustments */
            .topbar {
                padding: 0.5rem 1rem !important;
            }

            .topbar .nav-link {
                padding: 0.75rem 0.5rem !important;
                min-height: 44px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
            }

            /* Mobile Close Button Styling */
            .mobile-close-btn {
                position: absolute;
                top: 15px;
                right: 15px;
                width: 40px;
                height: 40px;
                background: rgba(50, 205, 50, 0.9) !important;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                display: flex !important;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                z-index: 1060;
                transition: all 0.3s ease;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            }

            .mobile-close-btn:hover {
                background: rgba(34, 139, 34, 0.95) !important;
                border-color: rgba(255, 255, 255, 0.5);
                transform: scale(1.05);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            }

            .mobile-close-btn i {
                color: white !important;
                font-size: 18px;
                font-weight: bold;
            }

            /* Hide close button on desktop */
            @media (min-width: 768px) {
                .mobile-close-btn {
                    display: none !important;
                }
            }
        }

        /* Text overflow handling for classroom cards */
        .classroom-card-text {
            overflow: hidden;
            word-wrap: break-word;
            word-break: break-word;
            hyphens: auto;
        }

        .classroom-card-text.truncate {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .classroom-card-text.truncate-single {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Specific styles for different text elements */
        .subject-text {
            max-width: 100%;
        }

        .section-text {
            max-width: 100%;
        }

        .details-text {
            max-width: 100%;
            max-height: 3em;
            line-height: 1.5em;
        }

        /* Tooltip styles for showing full text */
        .text-tooltip {
            position: relative;
            cursor: help;
        }

        .text-tooltip:hover::after {
            content: attr(data-full-text);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 0.875rem;
            white-space: normal;
            max-width: 300px;
            word-wrap: break-word;
            z-index: 1000;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        .text-tooltip:hover::before {
            content: '';
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%) translateY(100%);
            border: 5px solid transparent;
            border-top-color: rgba(0, 0, 0, 0.9);
            z-index: 1000;
        }

        /* Ensure consistent card heights */
        .card.h-100 {
            min-height: 200px;
            max-height: 300px;
        }

        .card-body {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .col.mr-2 {
            flex: 1;
            min-width: 0; /* Allow flex item to shrink below content size */
        }

        @media (max-width: 768px) {
            .card.h-100 {
                min-height: 180px;
                max-height: 280px;
            }

            .text-tooltip:hover::after {
                max-width: 250px;
                font-size: 0.8rem;
            }
        }
    </style>


    <!-- Javascripts-->
    <!-- Firebase configuration -->
    <script type="module">
        // Import the functions you need from the SDKs you need
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.11.1/firebase-app.js";
        import { getFirestore, collection, getDocs, deleteDoc, doc, updateDoc } from "https://www.gstatic.com/firebasejs/10.11.1/firebase-firestore.js";
        import { getAuth, deleteUser, onAuthStateChanged, setPersistence, browserLocalPersistence, signOut } from "https://www.gstatic.com/firebasejs/10.11.1/firebase-auth.js";

        // Session timeout duration (30 minutes in milliseconds)
        const SESSION_TIMEOUT = 30 * 60 * 1000;
        let sessionTimer;

        // Function to reset the session timer
        function resetSessionTimer() {
            if (sessionTimer) {
                clearTimeout(sessionTimer);
            }
            sessionTimer = setTimeout(() => {
                console.log('Session timed out due to inactivity');
                handleSessionTimeout();
            }, SESSION_TIMEOUT);
        }

        // Function to handle session timeout
        async function handleSessionTimeout() {
            try {
                // Clear localStorage
                localStorage.removeItem('adminData');
                // Sign out from Firebase
                await signOut(auth);
                // Redirect to login page
                alert('Your session has expired due to inactivity. Please log in again.');
                window.location.href = 'index.html';
            } catch (error) {
                console.error('Error during session timeout:', error);
            }
        }

        // Add event listeners for user activity
        const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
        activityEvents.forEach(event => {
            document.addEventListener(event, resetSessionTimer);
        });

        // Your web app's Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBuj8sMvbDKmjkAVG5JdVOdEF4OO9ijjzA",
            authDomain: "lead-login.firebaseapp.com",
            databaseURL: "https://lead-login-default-rtdb.firebaseio.com",
            projectId: "lead-login",
            storageBucket: "lead-login.firebasestorage.app",
            messagingSenderId: "1051456252675",
            appId: "1:1051456252675:web:61073e11903055f889d736"
        };

        // Initialize Firebase
        let app;
        let db;
        let auth;

        try {
            app = initializeApp(firebaseConfig);
            db = getFirestore(app);
            auth = getAuth(app);

            // Set persistence to LOCAL (this will maintain the session)
            setPersistence(auth, browserLocalPersistence)
                .then(() => {
                    console.log('Auth persistence set to LOCAL');
                    // Start session timer when persistence is set
                    resetSessionTimer();
                })
                .catch((error) => {
                    console.error('Error setting auth persistence:', error);
                });

            console.log('Firebase initialized successfully');

            // Make the Firestore instance available globally
            window.db = db;
            window.getDocs = getDocs;
            window.collection = collection;
            window.deleteDoc = deleteDoc;
            window.doc = doc;
            window.updateDoc = updateDoc; // Add updateDoc to window
            window.auth = auth;
            window.deleteUser = deleteUser;

            // Check if user is already logged in from localStorage
            const adminData = JSON.parse(localStorage.getItem('adminData'));
            if (adminData) {
                console.log('Found admin data in localStorage:', adminData);
                // Reset session timer when admin data is found
                resetSessionTimer();
            }

            // Add authentication check
            onAuthStateChanged(auth, async (user) => {
                console.log('Auth state changed. User:', user);

                if (!user) {
                    console.log('No user logged in, checking localStorage');
                    // Check localStorage for admin data
                    const adminData = JSON.parse(localStorage.getItem('adminData'));
                    if (!adminData) {
                        console.log('No admin data in localStorage, redirecting to login');
                        window.location.href = 'index.html';
                        return;
                    }
                    // If we have admin data in localStorage, allow access
                    console.log('Found admin data in localStorage, allowing access');
                    resetSessionTimer(); // Reset timer when access is granted
                    return;
                }

                console.log('User logged in, checking admin status for:', user.email);

                // Check if user is an admin
                const adminRef = collection(db, 'Admins');
                const adminSnapshot = await getDocs(adminRef);
                let isAdmin = false;

                adminSnapshot.forEach((doc) => {
                    const adminData = doc.data();
                    console.log('Checking admin:', adminData.email);
                    if (adminData.email === user.email) {
                        isAdmin = true;
                        console.log('Admin match found!');
                        // Store admin data in localStorage with timestamp
                        const adminDataWithTimestamp = {
                            ...adminData,
                            loginTimestamp: Date.now()
                        };
                        localStorage.setItem('adminData', JSON.stringify(adminDataWithTimestamp));
                        resetSessionTimer(); // Reset timer when admin status is verified
                    }
                });

                if (!isAdmin) {
                    console.log('User is not an admin, redirecting to login page');
                    localStorage.removeItem('adminData'); // Clear any existing admin data
                    alert('Unauthorized access. Only administrators can access this page.');
                    window.location.href = 'index.html';
                    return;
                }

                // User is authenticated and is an admin, allow access
                console.log('Admin access granted for:', user.email);
            });

            // Note: Logout handler is now handled by the modal-based system below

        } catch (error) {
            console.error('Error initializing Firebase:', error);
            // Only redirect if there's no admin data in localStorage
            if (!localStorage.getItem('adminData')) {
                window.location.href = 'index.html';
            }
        }
    </script>




</head>

<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        <ul class="navbar-nav bg-gradient-primary sidebar sidebar-dark accordion" id="accordionSidebar">

            <!-- Mobile Close Button -->
            <div class="mobile-close-btn d-block d-md-none" id="mobileSidebarClose">
                <i class="fas fa-chevron-left"></i>
            </div>

            <!-- Sidebar - Brand -->
            <a class="sidebar-brand d-flex align-items-center justify-content-center" href="#" id="dashboard-icon">
                <div class="sidebar-brand-icon">
                    <img src="assets/img/BINHSLogo.ico" alt="BINHS Logo" style="width: 30px; height: 30px;">
                </div>
                <div class="sidebar-brand-text mx-3">Dashboard</div>
            </a>

            <!-- Divider -->


            <!-- Nav Item - Dashboard -->
            <li class="nav-item active">
                <a class="nav-link" href="#" id="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </li>

            <!-- Divider
            <hr class="sidebar-divider">-->

            <!-- Heading -->
            <!--<div class="sidebar-heading">
                Interface
            </div> -->

            <!-- Nav Item - Pages Collapse Menu <li class="nav-item">
                <a class="nav-link collapsed" href="#" data-toggle="collapse" data-target="#collapseTwo"
                    aria-expanded="true" aria-controls="collapseTwo">
                    <i class="fas fa-fw fa-cog"></i>
                    <span>Post Materials</span>
                </a>
                <div id="collapseTwo" class="collapse" aria-labelledby="headingTwo" data-parent="#accordionSidebar">
                    <div class="bg-white py-2 collapse-inner rounded">
                        <h6 class="collapse-header">Custom Components:</h6>
                        <a class="collapse-item" href="buttons.html">Buttons</a>
                        <a class="collapse-item" href="cards.html">Cards</a>
                    </div>
                </div>
            </li>
            -->

            <!-- Nav Item - Utilities Collapse Menu <li class="nav-item">
                <button class="nav-link" id="system-nav" style="background: none; border: none; color: rgba(255, 255, 255, 0.8);">
                    <i class="fa-solid fa-chalkboard"></i>
                    <span>Manage Learning Materials</span>
                </button>
                <div id="collapseUtilities" class="collapse" aria-labelledby="headingUtilities"
                    data-parent="#accordionSidebar">
                    <div class="bg-white py-2 collapse-inner rounded">
                        <h6 class="collapse-header">Custom Utilities:</h6>
                        <a class="collapse-item" href="utilities-color.html">Colors</a>
                        <a class="collapse-item" href="utilities-border.html">Borders</a>
                        <a class="collapse-item" href="utilities-animation.html">Animations</a>
                        <a class="collapse-item" href="utilities-other.html">Other</a>
                    </div>
                </div>
            </li>-->



            <!-- Divider -->
            <hr class="sidebar-divider">

            <li class="nav-item">
                <a class="nav-link" href="Admin.html">
                    <i class="fa-solid fa-chalkboard"></i>
                    <span>Manage Sections</span>
                </a>

                <!--<div id="collapseTwo" class="collapse" aria-labelledby="headingTwo" data-parent="#accordionSidebar">
                    <div class="bg-white py-2 collapse-inner rounded">
                        <h6 class="collapse-header">Custom Components:</h6>
                        <a class="collapse-item" href="buttons.html">Buttons</a>
                        <a class="collapse-item" href="cards.html">Cards</a>
                    </div>
                </div>-->
            </li>


            <!-- Divider -->
            <hr class="sidebar-divider">


            <li class="nav-item">
                <a class="nav-link collapsed" href="#" data-toggle="collapse" data-target="#collapseTwo"
                    aria-expanded="true" aria-controls="collapseTwo">
                    <i class="fas fa-user-cog"></i>
                    <span>Manage Users</span>
                </a>
                <div id="collapseTwo" class="collapse" aria-labelledby="headingTwo" data-parent="#accordionSidebar">
                    <div class="bg-white py-2 collapse-inner rounded">
                        <h6 class="collapse-header">Manage:</h6>
                        <button id="manage-users-btn" class="collapse-item btn btn-link" >Students</button>
                        <button id="manage-teachers-btn" class="collapse-item btn btn-link">Teachers</button>
                    </div>
                </div>
            </li>

            <!-- Divider -->
            <hr class="sidebar-divider">

            <!-- Bulk Share Class Codes -->
            <li class="nav-item">
                <a class="nav-link" href="#" id="bulk-share-nav">
                    <i class="fas fa-share-alt"></i>
                    <span>Share Class Codes</span>
                </a>
            </li>



            <!-- Heading -->
            <!--<div class="sidebar-heading">
                Addons
            </div>-->

            <!-- Nav Item - Pages Collapse Menu <li class="nav-item">
                <a class="nav-link collapsed" href="#" data-toggle="collapse" data-target="#collapsePages"
                    aria-expanded="true" aria-controls="collapsePages">
                    <i class="fas fa-fw fa-folder"></i>
                    <span>Grades</span>
                </a>-->

            <!--<div id="collapsePages" class="collapse" aria-labelledby="headingPages" data-parent="#accordionSidebar">
                    <div class="bg-white py-2 collapse-inner rounded">
                        <h6 class="collapse-header">Login Screens:</h6>
                        <a class="collapse-item" href="login.html">Login</a>
                        <a class="collapse-item" href="register.html">Register</a>
                        <a class="collapse-item" href="forgot-password.html">Forgot Password</a>
                        <div class="collapse-divider"></div>
                        <h6 class="collapse-header">Other Pages:</h6>
                        <a class="collapse-item" href="404.html">404 Page</a>
                        <a class="collapse-item" href="blank.html">Blank Page</a>
                    </div>
                </div>-->
            </li>

            <!-- Nav Item - Charts <li class="nav-item">
                <a class="nav-link" href="charts.html">
                    <i class="fas fa-fw fa-chart-area"></i>
                    <span>Charts</span></a>
            </li>
-->

            <!-- Nav Item - Tables <li class="nav-item">
                <a class="nav-link" href="tables.html">
                    <i class="fas fa-fw fa-table"></i>
                    <span>Tables</span></a>
            </li>-->


            <!-- Divider
            <hr class="sidebar-divider d-none d-md-block">-->

            <!-- Sidebar Toggler (Sidebar) -->
            <div class="text-center">
                <button class="rounded-circle border-0" id="sidebarToggle"></button>
            </div>

            <!-- Sidebar Message
             <div class="sidebar-card d-none d-lg-flex">
                <img class="sidebar-card-illustration mb-2" src="img/undraw_rocket.svg" alt="...">
                <p class="text-center mb-2"><strong>SB Admin Pro</strong> is packed with premium features, components, and more!</p>
                <a class="btn btn-success btn-sm" href="https://startbootstrap.com/theme/sb-admin-pro">Upgrade to Pro!</a>
            </div>-->


        </ul>
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                <nav class="navbar navbar-expand navbar-light bg-white topbar mb-4 static-top shadow">

                    <!-- Sidebar Toggle (Topbar) -->
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="sidebarToggleTop">
                            <i class="fas fa-bars"></i>
                        </a>
                    </li>

                    <!-- Topbar Search<form
                        class="d-none d-sm-inline-block form-inline mr-auto ml-md-3 my-2 my-md-0 mw-100 navbar-search">
                        <div class="input-group">
                            <input type="text" class="form-control bg-light border-0 small" placeholder="Search for..."
                                aria-label="Search" aria-describedby="basic-addon2">
                            <div class="input-group-append">
                                <button class="btn btn-primary" type="button">
                                    <i class="fas fa-search fa-sm"></i>
                                </button>
                            </div>
                        </div>
                    </form> -->


                    <!-- Topbar Navbar -->
                    <ul class="navbar-nav ml-auto">



                        <!-- Nav Item - Alerts -->


                        <!-- Nav Item - Messages -->


                        <div class="topbar-divider d-none d-sm-block"></div>

                        <!-- Nav Item - User Information -->
                        <li class="nav-item dropdown no-arrow">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button"
                                data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <span class="mr-2 d-none d-lg-inline text-gray-600 small">Admin</span>
                                <i class="fas fa-cog fa-fw text-gray-400"></i>
                            </a>
                            <!-- Dropdown - User Information -->
                            <div class="dropdown-menu dropdown-menu-right shadow animated--grow-in"
                                aria-labelledby="userDropdown">
                                <div class="dropdown-header text-center">
                                    <div class="profile-info">
                                        <span id="profileName" class="font-weight-bold"></span>
                                        <div class="small text-gray-500" id="profileEmail"></div>
                                    </div>
                                </div>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="#" data-toggle="modal" data-target="#profileModal">
                                    <i class="fas fa-user fa-sm fa-fw mr-2 text-gray-400"></i>
                                    View Profile
                                </a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="#" data-toggle="modal" data-target="#logoutModal">
                                    <i class="fas fa-sign-out-alt fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Logout
                                </a>
                            </div>
                        </li>

                    </ul>

                </nav>
                <!-- End of Topbar -->







                <!-- Begin Page Content -->
                <div class="container-fluid" id="main-container">

                    <!-- Page Heading -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <div class="d-flex justify-content-between align-items-center flex-wrap">
                                <h6 class="m-0 font-weight-bold text-primary mb-2 mb-md-0">Manage Sections</h6>
                                <div class="d-flex align-items-center flex-wrap">
                                    <div class="mr-3 mb-2 mb-md-0">
                                        <label for="admin-sections-group-select" class="small text-muted mr-2">Group by:</label>
                                        <select id="admin-sections-group-select" class="form-control form-control-sm d-inline-block" style="width: auto; min-width: 120px;">
                                            <option value="none">No Grouping</option>
                                            <option value="subject">Subject</option>
                                            <option value="grade">Grade Level</option>
                                            <option value="course">Course</option>
                                            <option value="section">Section</option>
                                            <option value="teacher">Teacher</option>
                                        </select>
                                    </div>
                                    <div class="mr-3 mb-2 mb-md-0">
                                        <label for="admin-sections-sort-select" class="small text-muted mr-2">Sort by:</label>
                                        <select id="admin-sections-sort-select" class="form-control form-control-sm d-inline-block" style="width: auto; min-width: 150px;">
                                            <option value="name-asc">Name (A-Z)</option>
                                            <option value="name-desc">Name (Z-A)</option>
                                            <option value="date-desc">Date Created (Newest)</option>
                                            <option value="date-asc">Date Created (Oldest)</option>
                                            <option value="grade-asc">Grade Level (11-12)</option>
                                            <option value="grade-desc">Grade Level (12-11)</option>
                                            <option value="course-asc">Course (A-Z)</option>
                                            <option value="course-desc">Course (Z-A)</option>
                                            <option value="section-asc">Section (A-Z)</option>
                                            <option value="section-desc">Section (Z-A)</option>
                                            <option value="teacher-asc">Teacher (A-Z)</option>
                                            <option value="teacher-desc">Teacher (Z-A)</option>
                                            <option value="students-desc">Students (Most)</option>
                                            <option value="students-asc">Students (Least)</option>
                                        </select>
                                    </div>
                                    <button id="refresh-main" class="refresh-btn" title="Refresh Sections">
                                        <i class="fas fa-sync-alt"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="classroom-container" class="row"></div>
                        </div>
                    </div>

                    <style>
                        #second-container {
                            display: none; /* Hide second container by default */
                        }

                        /* Refresh button styles */
                        .refresh-btn {
                            background-color: #4e73df;
                            color: white;
                            border: none;
                            border-radius: 50%;
                            width: 36px;
                            height: 36px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            cursor: pointer;
                            transition: all 0.3s;
                            margin-left: 10px;
                        }

                        .refresh-btn:hover {
                            background-color: #2e59d9;
                            transform: rotate(30deg);
                        }

                        .refresh-btn i {
                            font-size: 16px;
                        }

                        .refresh-btn.spinning i {
                            animation: spin 1s linear infinite;
                        }

                        @keyframes spin {
                            0% { transform: rotate(0deg); }
                            100% { transform: rotate(360deg); }
                        }

                        /* Group header styling for admin sections */
                        .group-header {
                            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                            color: white;
                            padding: 12px 20px;
                            margin: 20px 0 15px 0;
                            border-radius: 8px;
                            font-weight: 600;
                            font-size: 1.1rem;
                            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                            position: relative;
                            cursor: pointer;
                            transition: all 0.2s ease;
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                        }

                        .group-header:hover {
                            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
                            transform: translateY(-1px);
                            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
                        }

                        .group-header:first-child {
                            margin-top: 0;
                        }

                        .group-header::before {
                            content: '';
                            position: absolute;
                            left: 0;
                            top: 0;
                            bottom: 0;
                            width: 4px;
                            background: rgba(255,255,255,0.3);
                            border-radius: 8px 0 0 8px;
                        }

                        .group-header .group-info {
                            display: flex;
                            align-items: center;
                            flex: 1;
                        }

                        .group-header .group-count {
                            background: rgba(255,255,255,0.2);
                            padding: 4px 8px;
                            border-radius: 12px;
                            font-size: 0.85rem;
                            margin-left: 10px;
                        }

                        .group-header .group-minimize-btn {
                            background: rgba(255,255,255,0.2);
                            border: none;
                            color: white;
                            width: 32px;
                            height: 32px;
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            cursor: pointer;
                            transition: all 0.2s ease;
                            font-size: 14px;
                            margin-left: 10px;
                        }

                        .group-header .group-minimize-btn:hover {
                            background: rgba(255,255,255,0.3);
                            transform: scale(1.1);
                        }

                        .group-header .group-minimize-btn:focus {
                            outline: none;
                            box-shadow: 0 0 0 2px rgba(255,255,255,0.3);
                        }

                        .group-header.collapsed .group-minimize-btn i {
                            transform: rotate(-90deg);
                        }

                        .group-content {
                            margin-bottom: 30px;
                            transition: all 0.3s ease;
                            opacity: 1;
                            max-height: none;
                            display: flex;
                            flex-wrap: wrap;
                            width: 100%;
                        }

                        .group-content.collapsed {
                            max-height: 0;
                            margin-bottom: 0;
                            opacity: 0;
                            padding: 0;
                            overflow: hidden;
                        }

                        .group-content .classroom-item {
                            margin-left: 0;
                            border-left: 3px solid #667eea;
                        }

                        .group-content .col-xl-3,
                        .group-content .col-md-6 {
                            margin-bottom: 20px;
                            opacity: 1;
                            visibility: visible;
                        }

                        .group-content .card {
                            opacity: 1;
                            visibility: visible;
                        }

                        /* Improved styles for classroom materials display */
                        .classroom-item {
                            transition: transform 0.2s ease, box-shadow 0.2s ease;
                        }

                        .classroom-item:hover {
                            transform: translateY(-2px);
                            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
                        }

                        @media (max-width: 768px) {
                            .card-header .d-flex {
                                flex-direction: column;
                                align-items: flex-start !important;
                                gap: 10px;
                            }

                            .group-header {
                                font-size: 1rem;
                                padding: 10px 15px;
                                flex-direction: column;
                                align-items: flex-start;
                                gap: 8px;
                            }

                            .group-header .group-info {
                                width: 100%;
                                justify-content: space-between;
                            }

                            .group-header .group-minimize-btn {
                                align-self: flex-end;
                                margin-left: 0;
                                margin-top: -32px;
                            }
                        }
                    </style>




























                    <style>
                        .hidden {
                            display: none;
                        }

                        .clickable-classroom {
                            cursor: default;
                            /* Makes the card NOT clickable */
                        }

                        /* Book Icon as Background */
                        .clickable-classroom::after {
                            content: "\f02d";
                            font-family: "Font Awesome 6 Free";
                            font-weight: 900;
                            position: absolute;
                            right: 15px;
                            bottom: 15px;
                            font-size: 60px;
                            color: rgba(0, 0, 0, 0.1);
                            pointer-events: none;
                        }

                        /* Three-dot menu */
                        .menu-btn {
                            position: absolute;
                            top: 10px;
                            right: 10px;
                            background: none;
                            border: none;
                            cursor: pointer;
                            font-size: 20px;
                        }

                        .card-menu {
                            position: absolute;
                            top: 30px;
                            right: 10px;
                            background: white;
                            border: 1px solid #ddd;
                            box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
                            padding: 5px;
                            border-radius: 5px;
                            z-index: 10;
                        }

                        .card-menu button {
                            display: block;
                            width: 100%;
                            border: none;
                            background: none;
                            padding: 5px;
                            cursor: pointer;
                        }

                        .card-menu button:hover {
                            background: #f0f0f0;
                        }



                        .hidden {
                            display: none;
                        }




                        /* Style for edit input fields */
                        .edit-input {
                            width: 100%;
                            /* Full width */
                            padding: 8px;
                            /* Comfortable padding */
                            margin-bottom: 10px;
                            /* Spacing between inputs */
                            border: 1px solid #ccc;
                            /* Light border */
                            border-radius: 4px;
                            /* Slightly rounded corners */
                            font-size: 14px;
                            /* Readable font size */
                            box-sizing: border-box;
                            /* Ensure padding is included in width */
                        }

                        /* Save and Cancel buttons */
                        .btn-success {
                            background-color: #28a745;
                            /* Green background */
                            color: white;
                            /* White text */
                            border: none;
                            /* Remove default border */
                            padding: 8px 16px;
                            /* Comfortable padding */
                            border-radius: 4px;
                            /* Rounded corners */
                            cursor: pointer;
                            /* Pointer cursor on hover */
                            font-size: 14px;
                            /* Readable font size */
                        }

                        .btn-secondary {
                            background-color: #6c757d;
                            /* Gray background */
                            color: white;
                            /* White text */
                            border: none;
                            /* Remove default border */
                            padding: 8px 16px;
                            /* Comfortable padding */
                            border-radius: 4px;
                            /* Rounded corners */
                            cursor: pointer;
                            /* Pointer cursor on hover */
                            font-size: 14px;
                            /* Readable font size */
                        }

                        /* Hover effects for buttons */
                        .btn-success:hover {
                            background-color: #218838;
                            /* Darker green on hover */
                        }

                        .btn-secondary:hover {
                            background-color: #5a6268;
                            /* Darker gray on hover */
                        }
                    </style>









                    <!-- End of New code -->













                    <!-- Content Row -->


                    <!-- Area Chart <div class="col-xl-8 col-lg-7">
                            <div class="card shadow mb-4">
                                <div
                                class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                                <h6 class="m-0 font-weight-bold text-primary">Earnings Overview</h6>
                                <div class="dropdown no-arrow">
                                    <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink"
                                        data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                                    </a>
                                    <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in"
                                        aria-labelledby="dropdownMenuLink">
                                        <div class="dropdown-header">Dropdown Header:</div>
                                        <a class="dropdown-item" href="#">Action</a>
                                        <a class="dropdown-item" href="#">Another action</a>
                                        <div class="dropdown-divider"></div>
                                        <a class="dropdown-item" href="#">Something else here</a>
                                    </div>
                                </div>
                            </div>

                            <div class="card-body">
                                <div class="chart-area">
                                    <canvas id="myAreaChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>-->


                    <!-- Pie Chart -->



                    <!-- Content Row -->


                </div>
                <!-- /.container-fluid -->

                <div class="container-fluid" id="second-container">
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <div class="d-flex align-items-center">
                            <h1 class="h3 mb-0 text-gray-800">List of Classrooms</h1>
                            <button id="refresh-classrooms" class="refresh-btn" title="Refresh Classroom List">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Classroom List Container -->
                    <div id="classroom-list" class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">All Classrooms</h6>
                        </div>
                        <div class="card-body">
                            <!-- Classrooms will be displayed here -->
                        </div>
                    </div>
                </div>


                <div class="container-fluid" id="third-container">
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <div class="d-flex align-items-center">
                            <h1 class="h3 mb-0 text-gray-800">List of Students</h1>
                            <button id="refresh-students" class="refresh-btn" title="Refresh Student List">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Students List Container -->
                    <div id="students-list" class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">All Students</h6>
                        </div>
                        <div class="card-body">
                            <!-- Students will be displayed here -->
                        </div>
                    </div>
                </div>


                <div class="container-fluid" id="fourth-container">
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <div class="d-flex align-items-center">
                            <h1 class="h3 mb-0 text-gray-800">List of Teachers</h1>
                            <button id="refresh-teachers" class="refresh-btn" title="Refresh Teacher List">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Classroom List Container -->
                    <div id="instructors-list" class="card shadow mb-4">


                    </div>


                </div>

                <!-- Bulk Share Class Codes Container -->
                <div class="container-fluid" id="bulk-share-container" style="display: none;">
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <div class="d-flex align-items-center">
                            <h1 class="h3 mb-0 text-gray-800">Bulk Share Class Codes</h1>
                            <button id="refresh-bulk-share" class="refresh-btn" title="Refresh Data">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Bulk Share Interface -->
                    <div class="row">
                        <!-- Classroom Selection -->
                        <div class="col-lg-6">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">Select Classrooms</h6>
                                </div>
                                <div class="card-body">
                                    <div class="form-group">
                                        <label for="classroom-filter">Filter by:</label>
                                        <div class="row">
                                            <div class="col-md-4">
                                                <select class="form-control form-control-sm" id="grade-filter">
                                                    <option value="">All Grades</option>
                                                    <option value="Grade 11">Grade 11</option>
                                                    <option value="Grade 12">Grade 12</option>
                                                </select>
                                            </div>
                                            <div class="col-md-4">
                                                <select class="form-control form-control-sm" id="section-filter">
                                                    <option value="">All Sections</option>
                                                </select>
                                            </div>
                                            <div class="col-md-4">
                                                <select class="form-control form-control-sm" id="strand-filter">
                                                    <option value="">All Strands</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="select-all-classrooms">
                                            <label class="form-check-label" for="select-all-classrooms">
                                                Select All Classrooms
                                            </label>
                                        </div>
                                    </div>
                                    <div id="classroom-selection" class="border rounded p-3" style="max-height: 300px; overflow-y: auto;">
                                        <!-- Classroom checkboxes will be populated here -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Student Selection -->
                        <div class="col-lg-6">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">Select Students</h6>
                                </div>
                                <div class="card-body">
                                    <div class="form-group">
                                        <label for="student-filter">Filter by:</label>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <select class="form-control form-control-sm" id="student-grade-filter">
                                                    <option value="">All Grades</option>
                                                    <option value="Grade 11">Grade 11</option>
                                                    <option value="Grade 12">Grade 12</option>
                                                </select>
                                            </div>
                                            <div class="col-md-6">
                                                <select class="form-control form-control-sm" id="student-status-filter">
                                                    <option value="">All Status</option>
                                                    <option value="approved">Approved</option>
                                                    <option value="pending">Pending</option>
                                                    <option value="rejected">Rejected</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="select-all-students">
                                            <label class="form-check-label" for="select-all-students">
                                                Select All Students
                                            </label>
                                        </div>
                                    </div>
                                    <div id="student-selection" class="border rounded p-3" style="max-height: 300px; overflow-y: auto;">
                                        <!-- Student checkboxes will be populated here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Message and Send Section -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">Send Class Codes</h6>
                                </div>
                                <div class="card-body">
                                    <div class="form-group">
                                        <label for="bulk-message">Custom Message (Optional):</label>
                                        <textarea class="form-control" id="bulk-message" rows="3"
                                                  placeholder="Enter a custom message to include with the class codes..."></textarea>
                                    </div>
                                    <div class="form-group">
                                        <label for="notification-label">Notification Label:</label>
                                        <input type="text" class="form-control" id="notification-label"
                                               value="Class Code Available" placeholder="Enter notification label">
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="alert alert-info">
                                                <strong>Selected Classrooms:</strong> <span id="selected-classrooms-count">0</span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="alert alert-info">
                                                <strong>Selected Students:</strong> <span id="selected-students-count">0</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="text-center">
                                        <button type="button" class="btn btn-primary btn-lg" id="send-bulk-codes">
                                            <i class="fas fa-paper-plane"></i> Send Class Codes to Students
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            <footer class="sticky-footer bg-white">
                <div class="container my-auto">
                    <div class="copyright text-center my-auto">
                        <span>Copyright &copy; LEAD LMS Website 2025</span>
                    </div>
                </div>
            </footer>
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                    <button class="btn btn-primary" id="confirmLogout">Logout</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap core JavaScript-->
    <script src="vendor/jquery/jquery.min.js"></script>
    <script src="vendor/bootstrap/js/bootstrap.bundle.min.js"></script>

    <!-- Core plugin JavaScript-->
    <script src="vendor/jquery-easing/jquery.easing.min.js"></script>

    <!-- Custom scripts for all pages-->
    <script src="js/sb-admin-2.min.js"></script>

    <!-- Mobile responsive script -->
    <script src="js/mobile-responsive.js"></script>

    <!-- Classroom management script -->
    <script type="module" src="js/classroomcards.js"></script>

    <!-- System navigation script -->
    <script type="module" src="js/system.js"></script>

    <!-- Classroom list script -->
    <script type="module" src="js/list-classrooms.js"></script>

    <!-- Debug script to check if system-nav exists -->
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const systemNav = document.getElementById('system-nav');
            console.log('Debug - system-nav element:', systemNav);

            // Debug mobile sidebar functionality
            const sidebar = document.querySelector('.sidebar');
            const sidebarToggleTop = document.getElementById('sidebarToggleTop');
            const overlay = document.querySelector('.sidebar-overlay');

            console.log('Debug - Mobile sidebar elements:');
            console.log('- Sidebar:', sidebar);
            console.log('- Toggle button:', sidebarToggleTop);
            console.log('- Overlay:', overlay);
            console.log('- MobileUtils available:', typeof window.MobileUtils);

            if (window.MobileUtils) {
                console.log('- MobileUtils.toggleSidebar:', typeof window.MobileUtils.toggleSidebar);
                console.log('- MobileUtils.closeSidebar:', typeof window.MobileUtils.closeSidebar);
            }

            // Test if sidebar toggle is working
            if (sidebarToggleTop) {
                console.log('Sidebar toggle button found, sb-admin-2.js should handle the click events');
                console.log('Mobile responsive enhancements will be applied via MutationObserver');
            }

            // Add mobile close button functionality
            const mobileCloseBtn = document.getElementById('mobileSidebarClose');
            if (mobileCloseBtn) {
                mobileCloseBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    console.log('Mobile close button clicked');

                    // Use jQuery to trigger the same event as sb-admin-2.js
                    if (typeof $ !== 'undefined') {
                        $('#sidebarToggleTop').trigger('click');
                    } else {
                        // Fallback: manually add the classes to hide sidebar
                        const sidebar = document.querySelector('.sidebar');
                        if (sidebar) {
                            sidebar.classList.add('toggled');
                            document.body.classList.add('sidebar-toggled');
                        }
                    }
                });
            }
        });
    </script>

    <!-- Add this script section before the closing body tag -->
    <script>
        // Add logout functionality
        document.getElementById('logout-btn').addEventListener('click', function(e) {
            e.preventDefault(); // Prevent default link behavior
            $('#logoutModal').modal('show'); // Show the logout modal
        });

        // Handle the confirm logout button click
        document.getElementById('confirmLogout').addEventListener('click', async function() {
            try {
                // Clear session timer if it exists
                if (window.sessionTimer) {
                    clearTimeout(window.sessionTimer);
                }

                // Clear localStorage data
                localStorage.removeItem('adminData');

                // Sign out from Firebase if auth is available
                if (window.auth && typeof window.auth.signOut === 'function') {
                    await window.auth.signOut();
                }

                // Redirect to login page
                window.location.href = 'index.html';
            } catch (error) {
                console.error('Error during logout:', error);
                // Even if there's an error, clear localStorage and redirect
                localStorage.clear();
                window.location.href = 'index.html';
            }
        });

        // Add dashboard icon logout confirmation
        document.getElementById('dashboard-icon').addEventListener('click', function(e) {
            e.preventDefault(); // Prevent default link behavior
            if (confirm('Navigating to the homepage will log you out of your current session. Do you want to proceed?')) {
                // Clear all localStorage data
                localStorage.clear();
                // Redirect to login page
                window.location.href = 'index.html';
            }
        });

        // Function to handle individual group minimize button clicks
        function handleGroupMinimize(e) {
            e.preventDefault();
            e.stopPropagation();

            const button = e.currentTarget;
            const header = button.closest('.group-header');

            if (!header) {
                return;
            }

            const content = header.nextElementSibling;

            if (content && content.classList.contains('group-content')) {
                // Toggle collapsed state
                const isCollapsed = content.classList.contains('collapsed');

                if (isCollapsed) {
                    // Expand
                    content.classList.remove('collapsed');
                    header.classList.remove('collapsed');
                    button.innerHTML = '<i class="fas fa-chevron-down"></i>';
                } else {
                    // Collapse
                    content.classList.add('collapsed');
                    header.classList.add('collapsed');
                    button.innerHTML = '<i class="fas fa-chevron-right"></i>';
                }

                // Save state to localStorage
                const groupName = header.textContent.trim().split('\n')[0].trim();
                const storageKey = `groupCollapsed_admin_${groupName}`;
                localStorage.setItem(storageKey, !isCollapsed);
            }
        }

        // Function to handle group minimize/expand functionality
        function initializeGroupMinimize() {
            // Add event listeners to all group headers with minimize buttons
            document.addEventListener('click', function(e) {
                // Check if the clicked element is a minimize button or inside one
                const button = e.target.closest('.group-minimize-btn');
                if (button) {
                    handleGroupMinimize(e);
                }
            });
        }

        // Function to restore group collapse states from localStorage
        function restoreGroupStates() {
            // Restore states for admin sections
            document.querySelectorAll('.group-header').forEach(header => {
                const groupName = header.textContent.trim().split('\n')[0].trim();
                const storageKey = `groupCollapsed_admin_${groupName}`;
                const isCollapsed = localStorage.getItem(storageKey) === 'true';

                if (isCollapsed) {
                    const content = header.nextElementSibling;
                    const button = header.querySelector('.group-minimize-btn');

                    if (content && button) {
                        content.classList.add('collapsed');
                        header.classList.add('collapsed');
                        button.innerHTML = '<i class="fas fa-chevron-right"></i>';
                    }
                }
            });
        }

        // Make functions available globally
        window.restoreGroupStates = restoreGroupStates;

        // Add sorting and grouping functionality for admin sections
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize group minimize functionality
            initializeGroupMinimize();
            // Add event listeners for admin sections sort and group dropdowns
            const adminSectionsSort = document.getElementById('admin-sections-sort-select');
            const adminSectionsGroup = document.getElementById('admin-sections-group-select');

            if (adminSectionsSort) {
                // Load saved sort preference
                const savedSort = localStorage.getItem('adminSectionsSortPreference');
                if (savedSort) {
                    adminSectionsSort.value = savedSort;
                }

                adminSectionsSort.addEventListener('change', function() {
                    console.log('Admin sections sort option changed to:', this.value);
                    // Save sort preference
                    localStorage.setItem('adminSectionsSortPreference', this.value);
                    // Trigger refresh of classroom display
                    triggerAdminClassroomRefresh();
                });
            }

            if (adminSectionsGroup) {
                // Load saved group preference
                const savedGroup = localStorage.getItem('adminSectionsGroupPreference');
                if (savedGroup) {
                    adminSectionsGroup.value = savedGroup;
                }

                adminSectionsGroup.addEventListener('change', function() {
                    console.log('Admin sections group option changed to:', this.value);
                    // Save group preference
                    localStorage.setItem('adminSectionsGroupPreference', this.value);
                    // Trigger refresh of classroom display
                    triggerAdminClassroomRefresh();
                });
            }

            // Function to trigger classroom refresh with admin-specific logic
            function triggerAdminClassroomRefresh() {
                // Clear the classroom container
                const container = document.getElementById('classroom-container');
                if (container) {
                    container.innerHTML = '';
                }

                // Call the loadCards function from classroomcards.js with admin preferences
                if (typeof window.loadCards === 'function') {
                    window.loadCards();
                } else {
                    // Fallback: dispatch a custom event and reload the script
                    const event = new CustomEvent('refreshAdminClassrooms');
                    document.dispatchEvent(event);

                    // Reload the classroomcards.js script
                    const script = document.createElement('script');
                    script.type = 'module';
                    script.src = 'js/classroomcards.js?' + new Date().getTime();
                    document.body.appendChild(script);
                }
            }

            // Make the refresh function available globally for the refresh button
            window.triggerAdminClassroomRefresh = triggerAdminClassroomRefresh;
        });

        // Add profile functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Get admin data from localStorage
            const adminData = JSON.parse(localStorage.getItem('adminData'));

            if (adminData) {
                // Update the profile dropdown with admin information
                document.querySelector('.mr-2.d-none.d-lg-inline.text-gray-600.small').textContent = `${adminData.firstName} ${adminData.lastName}`;
                document.getElementById('profileName').textContent = `${adminData.firstName} ${adminData.lastName}`;
                document.getElementById('profileEmail').textContent = adminData.email;

                // Update modal profile information
                document.getElementById('modalProfileName').textContent = `${adminData.firstName} ${adminData.lastName}`;
                document.getElementById('modalProfileEmail').textContent = adminData.email;
            }
        });

        async function showStudentsList() {
            console.log("showStudentsList function called"); // Debug log

            // Make sure we have access to the updateDoc function
            const updateDocFn = window.updateDoc || updateDoc;
            console.log("updateDoc function available:", typeof updateDocFn === 'function');

            try {
                // Get references to both collections
                const studentsRef = collection(db, 'Students');
                const enrollmentsRef = collection(db, 'Enrollments');

                console.log("Fetching students and enrollments data..."); // Debug log

                // Fetch both collections
                const [studentsSnapshot, enrollmentsSnapshot] = await Promise.all([
                    getDocs(studentsRef),
                    getDocs(enrollmentsRef)
                ]);

                if (!studentsSnapshot.empty) {
                    let html = '<div class="table-responsive"><table class="table table-bordered"><thead><tr><th>First Name</th><th>Last Name</th><th>Email</th><th>Grade Level</th><th>Status</th><th>Actions</th></tr></thead><tbody>';

                    // Create a map of enrollments by student email
                    const enrollmentsMap = new Map();
                    enrollmentsSnapshot.forEach(doc => {
                        const enrollment = doc.data();
                        if (enrollment.studentEmail) {
                            if (!enrollmentsMap.has(enrollment.studentEmail)) {
                                enrollmentsMap.set(enrollment.studentEmail, new Set());
                            }
                            enrollmentsMap.get(enrollment.studentEmail).add(enrollment.gradeLevel);
                        }
                    });

                    // Sort students by verification status (pending first) and then by creation date
                    const students = [];
                    studentsSnapshot.forEach((doc) => {
                        students.push({
                            id: doc.id,
                            data: doc.data()
                        });
                    });

                    // Sort students: pending first, then rejected, then approved, then those without status
                    students.sort((a, b) => {
                        const statusA = a.data.verificationStatus || 'approved'; // Default to approved for existing accounts
                        const statusB = b.data.verificationStatus || 'approved';

                        // First sort by status priority
                        const statusPriority = { 'pending': 0, 'rejected': 1, 'approved': 2 };
                        if (statusPriority[statusA] !== statusPriority[statusB]) {
                            return statusPriority[statusA] - statusPriority[statusB];
                        }

                        // If same status, sort by creation date (newest first)
                        const dateA = a.data.createdAt ? new Date(a.data.createdAt) : new Date(0);
                        const dateB = b.data.createdAt ? new Date(b.data.createdAt) : new Date(0);
                        return dateB - dateA;
                    });

                    // Loop through each student document
                    students.forEach((student) => {
                        const studentData = student.data;
                        const gradeLevels = enrollmentsMap.get(studentData.email);
                        const gradeLevelText = gradeLevels ? Array.from(gradeLevels).join(', ') : 'N/A';

                        // Determine verification status and style
                        const status = studentData.verificationStatus || 'approved'; // Default to approved for existing accounts
                        let statusBadge = '';

                        if (status === 'pending') {
                            statusBadge = '<span class="badge badge-warning">Pending Approval</span>';
                        } else if (status === 'approved') {
                            statusBadge = '<span class="badge badge-success">Approved</span>';
                        } else if (status === 'rejected') {
                            statusBadge = '<span class="badge badge-danger">Rejected</span>';
                        } else {
                            statusBadge = '<span class="badge badge-secondary">Unknown</span>';
                        }

                        // Determine which action buttons to show based on status
                        let actionButtons = '';

                        if (status === 'pending') {
                            actionButtons = `
                                <button class="btn btn-success btn-sm approve-student mb-1"
                                        data-student-id="${student.id}"
                                        data-student-email="${studentData.email}">
                                    <i class="fas fa-check"></i> Approve
                                </button>
                                <button class="btn btn-warning btn-sm reject-student mb-1"
                                        data-student-id="${student.id}"
                                        data-student-email="${studentData.email}">
                                    <i class="fas fa-ban"></i> Reject
                                </button>
                            `;
                        } else if (status === 'rejected') {
                            actionButtons = `
                                <button class="btn btn-success btn-sm approve-student mb-1"
                                        data-student-id="${student.id}"
                                        data-student-email="${studentData.email}">
                                    <i class="fas fa-check"></i> Approve
                                </button>
                            `;
                        } else if (status === 'approved') {
                            actionButtons = `
                                <button class="btn btn-warning btn-sm reject-student mb-1"
                                        data-student-id="${student.id}"
                                        data-student-email="${studentData.email}">
                                    <i class="fas fa-ban"></i> Reject
                                </button>
                            `;
                        }

                        // Always show delete button
                        actionButtons += `
                            <button class="btn btn-danger btn-sm delete-student"
                                    data-student-id="${student.id}"
                                    data-student-email="${studentData.email}">
                                <i class="fas fa-trash"></i> Delete
                            </button>
                        `;

                        html += `<tr>
                            <td>${studentData.firstName || 'N/A'}</td>
                            <td>${studentData.lastName || 'N/A'}</td>
                            <td>${studentData.email || 'N/A'}</td>
                            <td>${gradeLevelText}</td>
                            <td>${statusBadge}</td>
                            <td>
                                ${actionButtons}
                            </td>
                        </tr>`;
                    });

                    html += '</tbody></table></div>';

                    // Display the table in the students-list container
                    document.getElementById('students-list').innerHTML = html;

                    // Add event listeners to approve buttons
                    document.querySelectorAll('.approve-student').forEach(button => {
                        button.addEventListener('click', async function() {
                            const studentId = this.getAttribute('data-student-id');
                            const studentEmail = this.getAttribute('data-student-email');

                            if (confirm(`Are you sure you want to approve the account for ${studentEmail}?`)) {
                                try {
                                    console.log('Approving student with ID:', studentId);
                                    console.log('Student email:', studentEmail);

                                    // Update verification status in Firestore
                                    const studentRef = doc(db, 'Students', studentId);
                                    console.log('Student reference created:', studentRef);

                                    // Check if updateDoc is available
                                    if (typeof updateDocFn !== 'function') {
                                        console.error('updateDocFn is not a function!', typeof updateDocFn);
                                        alert('Error: updateDoc function is not available. Please refresh the page and try again.');
                                        return;
                                    }

                                    // Create the update data
                                    const updateData = {
                                        verificationStatus: 'approved',
                                        approvedAt: new Date().toISOString()
                                    };
                                    console.log('Update data:', updateData);

                                    // Perform the update using the function from the outer scope
                                    await updateDocFn(studentRef, updateData);
                                    console.log('Update successful');

                                    // Refresh the table
                                    showStudentsList();

                                    alert(`Student account for ${studentEmail} has been approved successfully!`);
                                } catch (error) {
                                    console.error('Error approving student:', error);
                                    console.error('Error details:', error.code, error.message);
                                    alert(`Error approving student account: ${error.message}. Please try again.`);
                                }
                            }
                        });
                    });

                    // Add event listeners to reject buttons
                    document.querySelectorAll('.reject-student').forEach(button => {
                        button.addEventListener('click', async function() {
                            const studentId = this.getAttribute('data-student-id');
                            const studentEmail = this.getAttribute('data-student-email');

                            if (confirm(`Are you sure you want to reject the account for ${studentEmail}?`)) {
                                try {
                                    console.log('Rejecting student with ID:', studentId);
                                    console.log('Student email:', studentEmail);

                                    // Update verification status in Firestore
                                    const studentRef = doc(db, 'Students', studentId);
                                    console.log('Student reference created:', studentRef);

                                    // Check if updateDoc is available
                                    if (typeof updateDocFn !== 'function') {
                                        console.error('updateDocFn is not a function!', typeof updateDocFn);
                                        alert('Error: updateDoc function is not available. Please refresh the page and try again.');
                                        return;
                                    }

                                    // Create the update data
                                    const updateData = {
                                        verificationStatus: 'rejected',
                                        rejectedAt: new Date().toISOString()
                                    };
                                    console.log('Update data:', updateData);

                                    // Perform the update using the function from the outer scope
                                    await updateDocFn(studentRef, updateData);
                                    console.log('Update successful');

                                    // Refresh the table
                                    showStudentsList();

                                    alert(`Student account for ${studentEmail} has been rejected.`);
                                } catch (error) {
                                    console.error('Error rejecting student:', error);
                                    console.error('Error details:', error.code, error.message);
                                    alert(`Error rejecting student account: ${error.message}. Please try again.`);
                                }
                            }
                        });
                    });

                    // Add event listeners to delete buttons
                    document.querySelectorAll('.delete-student').forEach(button => {
                        button.addEventListener('click', async function() {
                            const studentId = this.getAttribute('data-student-id');
                            const studentEmail = this.getAttribute('data-student-email');

                            // First confirmation
                            if (confirm('Are you sure you want to delete this student account? This action cannot be undone.')) {
                                // Second confirmation
                                if (confirm('This will permanently delete the student account from both the website and database. Are you absolutely sure?')) {
                                    try {
                                        // Delete from Firestore
                                        await deleteDoc(doc(db, 'Students', studentId));

                                        // Delete from Enrollments (if any)
                                        const enrollmentsQuery = await getDocs(collection(db, 'Enrollments'));
                                        enrollmentsQuery.forEach(async (enrollmentDoc) => {
                                            const enrollment = enrollmentDoc.data();
                                            if (enrollment.studentEmail === studentEmail) {
                                                await deleteDoc(doc(db, 'Enrollments', enrollmentDoc.id));
                                            }
                                        });

                                        // Refresh the table
                                        showStudentsList();

                                        alert('Student account deleted successfully!');
                                    } catch (error) {
                                        console.error('Error deleting student:', error);
                                        alert('Error deleting student account. Please try again.');
                                    }
                                }
                            }
                        });
                    });
                } else {
                    console.log("No data found"); // Debug log
                    document.getElementById('students-list').innerHTML = `
                        <div class="alert alert-info">
                            No students found in the database.
                        </div>
                    `;
                }
            } catch (error) {
                console.error("Error fetching data:", error);
                document.getElementById('students-list').innerHTML = `
                    <div class="alert alert-danger">
                        Error loading student data. Please try again later.
                    </div>
                `;
            }
        }

        // Call the function when the page loads
        document.addEventListener('DOMContentLoaded', showStudentsList);
    </script>

    <style>
        /* Sidebar Toggle Hover Style */
        #sidebarToggle .nav-link:hover {
            color: #105c1c !important;
        }

        /* Mobile sidebar toggle hover style */
        #sidebarToggleTop:hover {
            color: #105c1c !important;
        }
    </style>



    <!-- Refresh Buttons Functionality -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Main container refresh button (Classroom Cards)
            const refreshMainBtn = document.getElementById('refresh-main');
            if (refreshMainBtn) {
                refreshMainBtn.addEventListener('click', function() {
                    // Add spinning class
                    this.classList.add('spinning');

                    // Call the loadCards function from classroomcards.js
                    if (typeof loadCards === 'function') {
                        loadCards().then(() => {
                            // Remove spinning class after refresh
                            setTimeout(() => {
                                this.classList.remove('spinning');
                            }, 500);
                        }).catch(error => {
                            console.error('Error refreshing classroom cards:', error);
                            this.classList.remove('spinning');
                        });
                    } else {
                        // If loadCards is not directly accessible, try to reload the data
                        console.log('Attempting to reload classroom cards...');
                        // Clear the container
                        document.getElementById('classroom-container').innerHTML = '';

                        // Make the function available globally
                        window.refreshClassroomCards = function() {
                            refreshMainBtn.classList.remove('spinning');
                        };

                        // Create and dispatch a custom event
                        const event = new CustomEvent('refreshClassroomCards');
                        document.dispatchEvent(event);

                        // Fallback: if no handler catches the event, remove spinning after 2 seconds
                        setTimeout(() => {
                            if (refreshMainBtn.classList.contains('spinning')) {
                                refreshMainBtn.classList.remove('spinning');
                                location.reload(); // Reload the page as a last resort
                            }
                        }, 2000);
                    }
                });
            }

            // Classroom list refresh button
            const refreshClassroomsBtn = document.getElementById('refresh-classrooms');
            if (refreshClassroomsBtn) {
                refreshClassroomsBtn.addEventListener('click', function() {
                    // Add spinning class
                    this.classList.add('spinning');

                    // Try to call the displayClassrooms function from list-classrooms.js
                    if (typeof displayClassrooms === 'function') {
                        displayClassrooms().then(() => {
                            // Remove spinning class after refresh
                            setTimeout(() => {
                                this.classList.remove('spinning');
                            }, 500);
                        }).catch(error => {
                            console.error('Error refreshing classroom list:', error);
                            this.classList.remove('spinning');
                        });
                    } else {
                        // If displayClassrooms is not directly accessible
                        console.log('Attempting to reload classroom list...');

                        // Make the function available globally
                        window.refreshClassroomList = function() {
                            refreshClassroomsBtn.classList.remove('spinning');
                        };

                        // Create and dispatch a custom event
                        const event = new CustomEvent('refreshClassroomList');
                        document.dispatchEvent(event);

                        // Fallback: if no handler catches the event, remove spinning after 2 seconds
                        setTimeout(() => {
                            if (refreshClassroomsBtn.classList.contains('spinning')) {
                                refreshClassroomsBtn.classList.remove('spinning');
                                // Try to reload just this section
                                const script = document.createElement('script');
                                script.type = 'module';
                                script.src = 'js/list-classrooms.js?' + new Date().getTime();
                                document.body.appendChild(script);
                            }
                        }, 2000);
                    }
                });
            }

            // Students list refresh button
            const refreshStudentsBtn = document.getElementById('refresh-students');
            if (refreshStudentsBtn) {
                refreshStudentsBtn.addEventListener('click', function() {
                    // Add spinning class
                    this.classList.add('spinning');

                    // Call the showStudentsList function
                    if (typeof showStudentsList === 'function') {
                        showStudentsList().then(() => {
                            // Remove spinning class after refresh
                            setTimeout(() => {
                                this.classList.remove('spinning');
                            }, 500);
                        }).catch(error => {
                            console.error('Error refreshing students list:', error);
                            this.classList.remove('spinning');
                        });
                    } else {
                        console.error('showStudentsList function not found');
                        this.classList.remove('spinning');
                    }
                });
            }

            // Teachers list refresh button
            const refreshTeachersBtn = document.getElementById('refresh-teachers');
            if (refreshTeachersBtn) {
                refreshTeachersBtn.addEventListener('click', function() {
                    // Add spinning class
                    this.classList.add('spinning');

                    // Call the showTeachersList or showInstructorsList function
                    if (typeof showTeachersList === 'function') {
                        showTeachersList().then(() => {
                            // Remove spinning class after refresh
                            setTimeout(() => {
                                this.classList.remove('spinning');
                            }, 500);
                        }).catch(error => {
                            console.error('Error refreshing teachers list:', error);
                            this.classList.remove('spinning');
                        });
                    } else if (typeof showInstructorsList === 'function') {
                        showInstructorsList().then(() => {
                            // Remove spinning class after refresh
                            setTimeout(() => {
                                this.classList.remove('spinning');
                            }, 500);
                        }).catch(error => {
                            console.error('Error refreshing instructors list:', error);
                            this.classList.remove('spinning');
                        });
                    } else {
                        console.error('Teacher list refresh function not found');
                        this.classList.remove('spinning');
                    }
                });
            }
        });
    </script>

    <script>
        // Function to show third-container and hide the others
        document.getElementById('manage-users-btn').addEventListener('click', function () {
            document.getElementById('main-container').style.display = 'none';
            document.getElementById('second-container').style.display = 'none';
            document.getElementById('third-container').style.display = 'block';
            document.getElementById('fourth-container').style.display = 'none';
            showStudentsList(); // Call the function to display students
        });

        // Function to show fourth-container and hide the others
        document.getElementById('manage-teachers-btn').addEventListener('click', function () {
            document.getElementById('main-container').style.display = 'none';
            document.getElementById('second-container').style.display = 'none';
            document.getElementById('third-container').style.display = 'none';
            document.getElementById('fourth-container').style.display = 'block';
            showTeachersList(); // Call the function to display teachers
        });

        // Function to show bulk-share-container and hide the others
        const bulkShareNav = document.getElementById('bulk-share-nav');
        console.log('Bulk share nav element:', bulkShareNav); // Debug log

        if (bulkShareNav) {
            bulkShareNav.addEventListener('click', function (e) {
            e.preventDefault();
            console.log('Bulk share navigation clicked'); // Debug log

            // Hide all other containers
            document.getElementById('main-container').style.display = 'none';
            document.getElementById('second-container').style.display = 'none';
            document.getElementById('third-container').style.display = 'none';
            document.getElementById('fourth-container').style.display = 'none';

            // Show bulk share container
            document.getElementById('bulk-share-container').style.display = 'block';

            console.log('Bulk share container should now be visible'); // Debug log

            // Load bulk share data with a small delay to ensure DOM is ready
            setTimeout(() => {
                if (typeof window.loadBulkShareData === 'function') {
                    console.log('Loading bulk share data...'); // Debug log
                    window.loadBulkShareData();
                } else {
                    console.log('Bulk share data loading function not available yet');
                }
            }, 100);
            });
        } else {
            console.error('Bulk share navigation element not found!');
        }

        // Function to go back to main/second container from third-container
        document.addEventListener('click', function (e) {
            if (e.target && e.target.id === 'back-to-main') {
                document.getElementById('main-container').style.display = 'block';
                document.getElementById('second-container').style.display = 'block';
                document.getElementById('third-container').style.display = 'none';
                document.getElementById('fourth-container').style.display = 'none';
            }
        });

        // Hide third and fourth containers by default
        document.getElementById('third-container').style.display = 'none';
        document.getElementById('fourth-container').style.display = 'none';
    </script>

    <script>
        function showTeachersList() {
            console.log("showTeachersList function called"); // Debug log

            // Get a reference to the database
            const database = window.database;
            const ref = window.ref;
            const get = window.get;

            // Reference to the Teachers node
            const teachersRef = ref(database, 'Teachers');

            console.log("Fetching teachers data..."); // Debug log

            // Fetch the data
            get(teachersRef).then((snapshot) => {
                console.log("Data received:", snapshot.val()); // Debug log

                if (snapshot.exists()) {
                    const teachers = snapshot.val();
                    let html = '<div class="table-responsive"><table class="table table-bordered"><thead><tr><th>Name</th><th>Email</th><th>Grade Level</th></tr></thead><tbody>';

                    // Loop through each teacher
                    Object.values(teachers).forEach(teacher => {
                        html += `<tr>
                            <td>${teacher.name || 'N/A'}</td>
                            <td>${teacher.email || 'N/A'}</td>
                            <td>${teacher.gradeLevel || 'N/A'}</td>
                        </tr>`;
                    });

                    html += '</tbody></table></div>';

                    // Display the table in the fourth container
                    document.getElementById('fourth-container').innerHTML = `
                        <div class="d-sm-flex align-items-center justify-content-between mb-4">
                            <h1 class="h3 mb-0 text-gray-800">List of Teachers</h1>
                        </div>
                        ${html}
                    `;
                } else {
                    console.log("No data found"); // Debug log
                    document.getElementById('fourth-container').innerHTML = `
                        <div class="alert alert-info">
                            No teachers found in the database.
                        </div>
                    `;
                }
            }).catch((error) => {
                console.error("Error fetching teachers:", error);
                document.getElementById('fourth-container').innerHTML = `
                    <div class="alert alert-danger">
                        Error loading teacher data. Please try again later.
                    </div>
                `;
            });
        }
    </script>

    <style>
        /* Responsive table styles */
        .table-responsive {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        .table {
            width: 100%;
            margin-bottom: 1rem;
            background-color: transparent;
        }

        .table th {
            background-color: #f8f9fc;
            color: #5a5c69;
            font-weight: 600;
            padding: 1rem;
            white-space: nowrap;
        }

        .table td {
            padding: 1rem;
            vertical-align: middle;
        }

        /* Status badges */
        .badge {
            display: inline-block;
            padding: 0.35em 0.65em;
            font-size: 0.75em;
            font-weight: 700;
            line-height: 1;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 0.25rem;
        }

        .badge-success {
            color: #fff;
            background-color: #28a745;
        }

        .badge-warning {
            color: #212529;
            background-color: #ffc107;
        }

        .badge-danger {
            color: #fff;
            background-color: #dc3545;
        }

        .badge-secondary {
            color: #fff;
            background-color: #6c757d;
        }

        /* Action buttons */
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            white-space: nowrap;
            margin-right: 0.25rem;
        }

        .btn-sm i {
            font-size: 0.875rem;
        }

        /* Responsive action buttons */
        .delete-student, .approve-student, .reject-student,
        .delete-instructor, .approve-instructor, .reject-instructor {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            gap: 0.25rem;
            white-space: nowrap;
            margin-bottom: 0.25rem;
            width: 100%;
        }

        /* Mobile responsiveness */
        @media (max-width: 768px) {
            .table th, .table td {
                padding: 0.75rem;
            }

            .btn-sm, .delete-student, .approve-student, .reject-student,
            .delete-instructor, .approve-instructor, .reject-instructor {
                padding: 0.2rem 0.4rem;
                font-size: 0.75rem;
            }

            .btn-sm i, .delete-student i, .approve-student i, .reject-student i,
            .delete-instructor i, .approve-instructor i, .reject-instructor i {
                font-size: 0.75rem;
            }
        }

        @media (max-width: 576px) {
            .table th, .table td {
                padding: 0.5rem;
            }

            .btn-sm, .delete-student, .approve-student, .reject-student,
            .delete-instructor, .approve-instructor, .reject-instructor {
                padding: 0.15rem 0.3rem;
                font-size: 0.7rem;
            }

            .btn-sm i, .delete-student i, .approve-student i, .reject-student i,
            .delete-instructor i, .approve-instructor i, .reject-instructor i {
                font-size: 0.7rem;
            }
        }

        /* Hover effects */
        .delete-student:hover, .delete-instructor:hover {
            background-color: #dc3545;
            color: white;
            transform: scale(1.05);
            transition: all 0.2s ease;
        }

        .approve-student:hover, .approve-instructor:hover {
            background-color: #28a745;
            color: white;
            transform: scale(1.05);
            transition: all 0.2s ease;
        }

        .reject-student:hover, .reject-instructor:hover {
            background-color: #ffc107;
            color: #212529;
            transform: scale(1.05);
            transition: all 0.2s ease;
        }

        /* Table row hover effect */
        .table tbody tr:hover {
            background-color: rgba(0, 0, 0, 0.02);
        }
    </style>

    <script>
        // Function to generate a random 4-digit code for teacher ID
        function generateTeacherId() {
            return Math.floor(1000 + Math.random() * 9000).toString();
        }

        async function showInstructorsList() {
            console.log("showInstructorsList function called"); // Debug log

            // Make sure we have access to the updateDoc function
            const updateDocFn = window.updateDoc || updateDoc;
            console.log("updateDoc function available for instructors:", typeof updateDocFn === 'function');

            try {
                // Get reference to the Instructors collection
                const instructorsRef = collection(db, 'Instructors');

                console.log("Fetching instructors data..."); // Debug log

                // Fetch the data
                const instructorsSnapshot = await getDocs(instructorsRef);

                if (!instructorsSnapshot.empty) {
                    let html = '<div class="table-responsive"><table class="table table-bordered"><thead><tr><th>First Name</th><th>Last Name</th><th>Email</th><th>Status</th><th>Teacher ID</th><th>Actions</th></tr></thead><tbody>';

                    // Sort instructors by verification status (pending first) and then by creation date
                    const instructors = [];
                    instructorsSnapshot.forEach((doc) => {
                        instructors.push({
                            id: doc.id,
                            data: doc.data()
                        });
                    });

                    // Sort instructors: pending first, then rejected, then approved, then those without status
                    instructors.sort((a, b) => {
                        const statusA = a.data.verificationStatus || 'approved'; // Default to approved for existing accounts
                        const statusB = b.data.verificationStatus || 'approved';

                        // First sort by status priority
                        const statusPriority = { 'pending': 0, 'rejected': 1, 'approved': 2 };
                        if (statusPriority[statusA] !== statusPriority[statusB]) {
                            return statusPriority[statusA] - statusPriority[statusB];
                        }

                        // If same status, sort by creation date (newest first)
                        const dateA = a.data.createdAt ? new Date(a.data.createdAt) : new Date(0);
                        const dateB = b.data.createdAt ? new Date(b.data.createdAt) : new Date(0);
                        return dateB - dateA;
                    });

                    // Loop through each instructor document
                    instructors.forEach((instructor) => {
                        const instructorData = instructor.data;

                        // Determine verification status and style
                        const status = instructorData.verificationStatus || 'approved'; // Default to approved for existing accounts
                        let statusBadge = '';

                        if (status === 'pending') {
                            statusBadge = '<span class="badge badge-warning">Pending Approval</span>';
                        } else if (status === 'approved') {
                            statusBadge = '<span class="badge badge-success">Approved</span>';
                        } else if (status === 'rejected') {
                            statusBadge = '<span class="badge badge-danger">Rejected</span>';
                        } else {
                            statusBadge = '<span class="badge badge-secondary">Unknown</span>';
                        }

                        // Determine which action buttons to show based on status
                        let actionButtons = '';

                        if (status === 'pending') {
                            actionButtons = `
                                <button class="btn btn-success btn-sm approve-instructor mb-1"
                                        data-instructor-id="${instructor.id}"
                                        data-instructor-email="${instructorData.email}">
                                    <i class="fas fa-check"></i> Approve
                                </button>
                                <button class="btn btn-warning btn-sm reject-instructor mb-1"
                                        data-instructor-id="${instructor.id}"
                                        data-instructor-email="${instructorData.email}">
                                    <i class="fas fa-ban"></i> Reject
                                </button>
                            `;
                        } else if (status === 'rejected') {
                            actionButtons = `
                                <button class="btn btn-success btn-sm approve-instructor mb-1"
                                        data-instructor-id="${instructor.id}"
                                        data-instructor-email="${instructorData.email}">
                                    <i class="fas fa-check"></i> Approve
                                </button>
                            `;
                        } else if (status === 'approved') {
                            actionButtons = `
                                <button class="btn btn-warning btn-sm reject-instructor mb-1"
                                        data-instructor-id="${instructor.id}"
                                        data-instructor-email="${instructorData.email}">
                                    <i class="fas fa-ban"></i> Reject
                                </button>
                            `;
                        }

                        // Always show delete button
                        actionButtons += `
                            <button class="btn btn-danger btn-sm delete-instructor"
                                    data-instructor-id="${instructor.id}"
                                    data-instructor-email="${instructorData.email}">
                                <i class="fas fa-trash"></i> Delete
                            </button>
                        `;

                        // Display teacher ID if approved and ID exists
                        const teacherId = instructorData.teacherId || (status === 'approved' ? 'Not generated' : 'N/A');
                        const teacherIdDisplay = status === 'approved' ?
                            (instructorData.teacherId ?
                                `<span class="badge badge-info">${instructorData.teacherId}</span>` :
                                '<span class="badge badge-secondary">Not generated</span>') :
                            '<span class="badge badge-secondary">N/A</span>';

                        html += `<tr>
                            <td>${instructorData.firstName || 'N/A'}</td>
                            <td>${instructorData.lastName || 'N/A'}</td>
                            <td>${instructorData.email || 'N/A'}</td>
                            <td>${statusBadge}</td>
                            <td>${teacherIdDisplay}</td>
                            <td>
                                ${actionButtons}
                            </td>
                        </tr>`;
                    });

                    html += '</tbody></table></div>';

                    // Display the table in the instructors-list container
                    document.getElementById('instructors-list').innerHTML = html;

                    // Add event listeners to approve buttons
                    document.querySelectorAll('.approve-instructor').forEach(button => {
                        button.addEventListener('click', async function() {
                            const instructorId = this.getAttribute('data-instructor-id');
                            const instructorEmail = this.getAttribute('data-instructor-email');

                            if (confirm(`Are you sure you want to approve the account for ${instructorEmail}?`)) {
                                try {
                                    console.log('Approving instructor with ID:', instructorId);
                                    console.log('Instructor email:', instructorEmail);

                                    // Update verification status in Firestore
                                    const instructorRef = doc(db, 'Instructors', instructorId);
                                    console.log('Instructor reference created:', instructorRef);

                                    // Check if updateDoc is available
                                    if (typeof updateDocFn !== 'function') {
                                        console.error('updateDocFn is not a function!', typeof updateDocFn);
                                        alert('Error: updateDoc function is not available. Please refresh the page and try again.');
                                        return;
                                    }

                                    // Generate a random 4-digit teacher ID
                                    const teacherId = generateTeacherId();
                                    console.log('Generated teacher ID:', teacherId);

                                    // Create the update data
                                    const updateData = {
                                        verificationStatus: 'approved',
                                        approvedAt: new Date().toISOString(),
                                        teacherId: teacherId
                                    };
                                    console.log('Update data:', updateData);

                                    // Perform the update using the function from the outer scope
                                    await updateDocFn(instructorRef, updateData);
                                    console.log('Update successful');

                                    // Refresh the table
                                    showInstructorsList();

                                    alert(`Instructor account for ${instructorEmail} has been approved successfully! A teacher ID (${teacherId}) has been automatically generated for this account.`);
                                } catch (error) {
                                    console.error('Error approving instructor:', error);
                                    console.error('Error details:', error.code, error.message);
                                    alert(`Error approving instructor account: ${error.message}. Please try again.`);
                                }
                            }
                        });
                    });

                    // Add event listeners to reject buttons
                    document.querySelectorAll('.reject-instructor').forEach(button => {
                        button.addEventListener('click', async function() {
                            const instructorId = this.getAttribute('data-instructor-id');
                            const instructorEmail = this.getAttribute('data-instructor-email');

                            if (confirm(`Are you sure you want to reject the account for ${instructorEmail}?`)) {
                                try {
                                    console.log('Rejecting instructor with ID:', instructorId);
                                    console.log('Instructor email:', instructorEmail);

                                    // Update verification status in Firestore
                                    const instructorRef = doc(db, 'Instructors', instructorId);
                                    console.log('Instructor reference created:', instructorRef);

                                    // Check if updateDoc is available
                                    if (typeof updateDocFn !== 'function') {
                                        console.error('updateDocFn is not a function!', typeof updateDocFn);
                                        alert('Error: updateDoc function is not available. Please refresh the page and try again.');
                                        return;
                                    }

                                    // Create the update data
                                    const updateData = {
                                        verificationStatus: 'rejected',
                                        rejectedAt: new Date().toISOString()
                                    };
                                    console.log('Update data:', updateData);

                                    // Perform the update using the function from the outer scope
                                    await updateDocFn(instructorRef, updateData);
                                    console.log('Update successful');

                                    // Refresh the table
                                    showInstructorsList();

                                    alert(`Instructor account for ${instructorEmail} has been rejected.`);
                                } catch (error) {
                                    console.error('Error rejecting instructor:', error);
                                    console.error('Error details:', error.code, error.message);
                                    alert(`Error rejecting instructor account: ${error.message}. Please try again.`);
                                }
                            }
                        });
                    });

                    // Add event listeners to delete buttons
                    document.querySelectorAll('.delete-instructor').forEach(button => {
                        button.addEventListener('click', async function() {
                            const instructorId = this.getAttribute('data-instructor-id');
                            const instructorEmail = this.getAttribute('data-instructor-email');

                            // First confirmation
                            if (confirm('Are you sure you want to delete this instructor account? This action cannot be undone.')) {
                                // Second confirmation
                                if (confirm('This will permanently delete the instructor account from both the website and database. Are you absolutely sure?')) {
                                    try {
                                        // Delete from Firestore
                                        await deleteDoc(doc(db, 'Instructors', instructorId));

                                        // Refresh the table
                                        showInstructorsList();

                                        alert('Instructor account deleted successfully!');
                                    } catch (error) {
                                        console.error('Error deleting instructor:', error);
                                        alert('Error deleting instructor account. Please try again.');
                                    }
                                }
                            }
                        });
                    });
                } else {
                    console.log("No data found"); // Debug log
                    document.getElementById('instructors-list').innerHTML = `
                        <div class="alert alert-info">
                            No instructors found in the database.
                        </div>
                    `;
                }
            } catch (error) {
                console.error("Error fetching data:", error);
                document.getElementById('instructors-list').innerHTML = `
                    <div class="alert alert-danger">
                        Error loading instructor data. Please try again later.
                    </div>
                `;
            }
        }

        // Call the function when the page loads
        document.addEventListener('DOMContentLoaded', function() {
            showStudentsList();
            showInstructorsList();
        });
    </script>

    <!-- Profile Modal -->
    <div class="modal fade" id="profileModal" tabindex="-1" role="dialog" aria-labelledby="profileModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="profileModalLabel">Admin Profile</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">Personal Information</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-sm-3">
                                            <p class="mb-0">Full Name</p>
                                        </div>
                                        <div class="col-sm-9">
                                            <p class="text-muted mb-0" id="modalProfileName"></p>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row mb-3">
                                        <div class="col-sm-3">
                                            <p class="mb-0">Email</p>
                                        </div>
                                        <div class="col-sm-9">
                                            <p class="text-muted mb-0" id="modalProfileEmail"></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Return</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Responsive JavaScript -->
    <script src="js/mobile-responsive.js"></script>

    <!-- Bulk Share Class Codes JavaScript -->
    <script type="module">
        // Import Firebase functions
        import { collection, getDocs, addDoc, query, where, serverTimestamp } from "https://www.gstatic.com/firebasejs/10.11.1/firebase-firestore.js";

        // Global variables for bulk sharing
        let allClassrooms = [];
        let allStudents = [];
        let selectedClassrooms = new Set();
        let selectedStudents = new Set();

        // Load classrooms and students data
        async function loadBulkShareData() {
            try {
                await Promise.all([loadClassrooms(), loadStudents()]);
                populateFilters();
                displayClassrooms();
                displayStudents();
            } catch (error) {
                console.error('Error loading bulk share data:', error);
                alert('Error loading data. Please try again.');
            }
        }

        // Load all classrooms
        async function loadClassrooms() {
            try {
                const querySnapshot = await getDocs(collection(window.db, 'Classrooms'));
                allClassrooms = [];
                querySnapshot.forEach((doc) => {
                    allClassrooms.push({ id: doc.id, ...doc.data() });
                });
                console.log('Loaded classrooms:', allClassrooms.length);
            } catch (error) {
                console.error('Error loading classrooms:', error);
                throw error;
            }
        }

        // Load all students
        async function loadStudents() {
            try {
                const querySnapshot = await getDocs(collection(window.db, 'Students'));
                allStudents = [];
                querySnapshot.forEach((doc) => {
                    allStudents.push({ id: doc.id, ...doc.data() });
                });
                console.log('Loaded students:', allStudents.length);
            } catch (error) {
                console.error('Error loading students:', error);
                throw error;
            }
        }

        // Populate filter dropdowns
        function populateFilters() {
            // Populate section filter
            const sections = [...new Set(allClassrooms.map(c => c.sectionName).filter(s => s))];
            const sectionFilter = document.getElementById('section-filter');
            sectionFilter.innerHTML = '<option value="">All Sections</option>';
            sections.forEach(section => {
                sectionFilter.innerHTML += `<option value="${section}">${section}</option>`;
            });

            // Populate strand filter
            const strands = [...new Set(allClassrooms.map(c => c.course).filter(s => s))];
            const strandFilter = document.getElementById('strand-filter');
            strandFilter.innerHTML = '<option value="">All Strands</option>';
            strands.forEach(strand => {
                strandFilter.innerHTML += `<option value="${strand}">${strand}</option>`;
            });
        }

        // Display classrooms with checkboxes
        function displayClassrooms() {
            const container = document.getElementById('classroom-selection');
            const gradeFilter = document.getElementById('grade-filter').value;
            const sectionFilter = document.getElementById('section-filter').value;
            const strandFilter = document.getElementById('strand-filter').value;

            let filteredClassrooms = allClassrooms.filter(classroom => {
                return (!gradeFilter || classroom.gradeLevel === gradeFilter) &&
                       (!sectionFilter || classroom.sectionName === sectionFilter) &&
                       (!strandFilter || classroom.course === strandFilter);
            });

            let html = '';
            filteredClassrooms.forEach(classroom => {
                const isChecked = selectedClassrooms.has(classroom.id) ? 'checked' : '';
                html += `
                    <div class="form-check mb-2">
                        <input class="form-check-input classroom-checkbox" type="checkbox"
                               value="${classroom.id}" id="classroom-${classroom.id}" ${isChecked}>
                        <label class="form-check-label" for="classroom-${classroom.id}">
                            <strong>${classroom.subjectName}</strong><br>
                            <small class="text-muted">
                                ${classroom.gradeLevel} - ${classroom.sectionName || 'No Section'} - ${classroom.course}<br>
                                Code: <span class="text-primary font-weight-bold">${classroom.enrollCode}</span>
                            </small>
                        </label>
                    </div>
                `;
            });

            if (html === '') {
                html = '<p class="text-muted">No classrooms found matching the filters.</p>';
            }

            container.innerHTML = html;
            updateSelectedCount();
        }

        // Display students with checkboxes
        function displayStudents() {
            const container = document.getElementById('student-selection');
            const gradeFilter = document.getElementById('student-grade-filter').value;
            const statusFilter = document.getElementById('student-status-filter').value;

            let filteredStudents = allStudents.filter(student => {
                const status = student.verificationStatus || 'approved';
                return (!gradeFilter || student.gradeLevel === gradeFilter) &&
                       (!statusFilter || status === statusFilter);
            });

            let html = '';
            filteredStudents.forEach(student => {
                const isChecked = selectedStudents.has(student.id) ? 'checked' : '';
                const status = student.verificationStatus || 'approved';
                const statusBadge = status === 'approved' ? 'success' :
                                   status === 'pending' ? 'warning' : 'danger';

                html += `
                    <div class="form-check mb-2">
                        <input class="form-check-input student-checkbox" type="checkbox"
                               value="${student.id}" id="student-${student.id}" ${isChecked}>
                        <label class="form-check-label" for="student-${student.id}">
                            <strong>${student.firstName} ${student.lastName}</strong>
                            <span class="badge badge-${statusBadge} ml-2">${status}</span><br>
                            <small class="text-muted">
                                ${student.email}<br>
                                ${student.gradeLevel || 'No Grade'}
                            </small>
                        </label>
                    </div>
                `;
            });

            if (html === '') {
                html = '<p class="text-muted">No students found matching the filters.</p>';
            }

            container.innerHTML = html;
            updateSelectedCount();
        }

        // Update selected counts
        function updateSelectedCount() {
            document.getElementById('selected-classrooms-count').textContent = selectedClassrooms.size;
            document.getElementById('selected-students-count').textContent = selectedStudents.size;
        }

        // Event listeners for filters
        document.getElementById('grade-filter').addEventListener('change', displayClassrooms);
        document.getElementById('section-filter').addEventListener('change', displayClassrooms);
        document.getElementById('strand-filter').addEventListener('change', displayClassrooms);
        document.getElementById('student-grade-filter').addEventListener('change', displayStudents);
        document.getElementById('student-status-filter').addEventListener('change', displayStudents);

        // Event listeners for select all checkboxes
        document.getElementById('select-all-classrooms').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.classroom-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
                if (this.checked) {
                    selectedClassrooms.add(checkbox.value);
                } else {
                    selectedClassrooms.delete(checkbox.value);
                }
            });
            updateSelectedCount();
        });

        document.getElementById('select-all-students').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.student-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
                if (this.checked) {
                    selectedStudents.add(checkbox.value);
                } else {
                    selectedStudents.delete(checkbox.value);
                }
            });
            updateSelectedCount();
        });

        // Event delegation for individual checkboxes
        document.getElementById('classroom-selection').addEventListener('change', function(e) {
            if (e.target.classList.contains('classroom-checkbox')) {
                if (e.target.checked) {
                    selectedClassrooms.add(e.target.value);
                } else {
                    selectedClassrooms.delete(e.target.value);
                }
                updateSelectedCount();
            }
        });

        document.getElementById('student-selection').addEventListener('change', function(e) {
            if (e.target.classList.contains('student-checkbox')) {
                if (e.target.checked) {
                    selectedStudents.add(e.target.value);
                } else {
                    selectedStudents.delete(e.target.value);
                }
                updateSelectedCount();
            }
        });

        // Refresh button handler
        document.getElementById('refresh-bulk-share').addEventListener('click', function() {
            this.classList.add('spinning');
            loadBulkShareData().then(() => {
                setTimeout(() => {
                    this.classList.remove('spinning');
                }, 500);
            }).catch(() => {
                this.classList.remove('spinning');
            });
        });

        // Send bulk codes handler
        document.getElementById('send-bulk-codes').addEventListener('click', async function() {
            if (selectedClassrooms.size === 0) {
                alert('Please select at least one classroom.');
                return;
            }

            if (selectedStudents.size === 0) {
                alert('Please select at least one student.');
                return;
            }

            const message = document.getElementById('bulk-message').value;
            const label = document.getElementById('notification-label').value || 'Class Code Available';

            if (!confirm(`Are you sure you want to send ${selectedClassrooms.size} class codes to ${selectedStudents.size} students?`)) {
                return;
            }

            try {
                this.disabled = true;
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';

                await sendBulkClassCodes(message, label);

                alert('Class codes sent successfully!');

                // Reset selections
                selectedClassrooms.clear();
                selectedStudents.clear();
                document.getElementById('bulk-message').value = '';
                document.getElementById('select-all-classrooms').checked = false;
                document.getElementById('select-all-students').checked = false;
                displayClassrooms();
                displayStudents();

            } catch (error) {
                console.error('Error sending bulk codes:', error);
                alert('Error sending class codes. Please try again.');
            } finally {
                this.disabled = false;
                this.innerHTML = '<i class="fas fa-paper-plane"></i> Send Class Codes to Students';
            }
        });

        // Function to send bulk class codes
        async function sendBulkClassCodes(message, label) {
            const adminData = JSON.parse(localStorage.getItem('adminData'));
            if (!adminData) {
                throw new Error('Admin data not found');
            }

            const notifications = [];

            // Create notifications for each combination of classroom and student
            for (const classroomId of selectedClassrooms) {
                const classroom = allClassrooms.find(c => c.id === classroomId);
                if (!classroom) continue;

                for (const studentId of selectedStudents) {
                    const student = allStudents.find(s => s.id === studentId);
                    if (!student) continue;

                    const notificationData = {
                        type: 'classCode',
                        classroomId: classroomId,
                        classroomName: classroom.subjectName,
                        enrollCode: classroom.enrollCode,
                        message: message,
                        label: label,
                        sentBy: adminData.email,
                        sentByName: `${adminData.firstName} ${adminData.lastName}`,
                        sentAt: serverTimestamp(),
                        isRead: false,
                        // Inherited classroom details for sorting
                        gradeLevel: classroom.gradeLevel,
                        sectionName: classroom.sectionName,
                        course: classroom.course,
                        // Student information for targeting
                        studentId: studentId,
                        studentEmail: student.email
                    };

                    notifications.push(notificationData);
                }
            }

            // Send all notifications to Firestore
            const notificationsRef = collection(window.db, 'ClassCodeNotifications');
            const promises = notifications.map(notification => addDoc(notificationsRef, notification));
            await Promise.all(promises);

            console.log(`Sent ${notifications.length} class code notifications`);
        }

        // Make functions available globally for navigation handler
        window.loadBulkShareData = loadBulkShareData;
    </script>

</body>

</html>